# SideView - Sidebar Browser Panel

A modern sidebar browser application that provides instant access to your favorite web applications. Built with Electron and TypeScript for a native desktop experience with seamless web app integration.

## 🚀 Features

- **Sliding Sidebar Panel**: Smooth slide-out panel from screen edge with hover activation
- **Multiple Web Apps**: Host multiple web applications with session isolation
- **Global Hotkeys**: System-wide keyboard shortcuts (Ctrl+Alt+Right by default)
- **Native Notifications**: Web notifications bridged to system native toasts
- **Automatic Updates**: GitHub-based update system
- **Session Management**: Isolated cookies/cache per app or shared sessions
- **Favicon Support**: Automatic favicon fetching and caching
- **Theme Support**: Dark/light mode with system theme detection
- **Performance Optimized**: Fast startup and efficient memory usage

## 🎯 Quick Start

### Prerequisites
- **Node.js 18+** (Download from [nodejs.org](https://nodejs.org/))
- **Git** (Download from [git-scm.com](https://git-scm.com/))

### Installation & First Run

1. **Clone the repository**:
   ```bash
   git clone https://github.com/sideview/sideview.git
   cd sideview
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Build and run**:
   ```bash
   npm run build
   npm run dev
   ```

### Adding Your First App
1. **Launch SideView** and press `Ctrl+Alt+Right` to open the panel
2. **Add Your First App**:
   - Click the "+" button in the sidebar
   - Enter a web app URL (e.g., `https://gmail.com`)
   - Choose session isolation (recommended for separate accounts)
   - Click "Add App"
3. **Navigate**: Click on your app to load it in the sidebar
4. **Pin Panel**: Click the pin icon to keep the panel open

### Default Hotkeys
- `Ctrl+Alt+Right`: Toggle sidebar visibility
- `Ctrl+Alt+Left`: Toggle sidebar visibility (alternative)

### Popular Apps to Try
- **Gmail**: `https://mail.google.com`
- **Outlook**: `https://outlook.office.com`
- **Slack**: `https://app.slack.com`
- **Discord**: `https://discord.com/app`
- **WhatsApp Web**: `https://web.whatsapp.com`
- **Telegram Web**: `https://web.telegram.org`
- **Notion**: `https://notion.so`

## 📖 User Guide

### Panel Activation
- **Mouse**: Hover over the screen edge (configurable)
- **Hotkey**: Press `Ctrl+Alt+Right` (configurable)
- **Always On**: Use the pin button to keep panel visible

### Managing Apps
- **Add App**: Click "+" button, enter URL, configure isolation
- **Remove App**: Right-click app → Delete
- **Reorder Apps**: Drag and drop in the app list
- **Edit App**: Right-click app → Edit

### Session Isolation Options
- **Isolated**: Separate cookies/cache per app (recommended for multiple accounts)
- **Shared**: Common session data across all apps

### Notifications
- **Web Notifications**: Automatically bridged to system notifications
- **Badge Counts**: Unread indicators on app icons
- **Permission Management**: Grant/deny notification permissions per app

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev              # Build and run in development mode
npm run dev:watch        # Run with file watching and auto-reload

# Building
npm run build            # Build both main and renderer processes
npm run build:main       # Build main process only
npm run build:renderer   # Build renderer process only

# Testing
npm run test             # Run tests
npm run test:watch       # Run tests in watch mode

# Linting
npm run lint             # Check code style
npm run lint:fix         # Fix code style issues

# Distribution
npm run pack             # Package app (unpacked)
npm run dist             # Build distributable packages
npm run dist:win         # Build Windows installer
```


### Project Structure

```
sideview/
├── src/
│   ├── main/                 # Main Electron process
│   │   ├── main.ts          # Application entry point
│   │   └── app-host.ts      # Application host logic
│   ├── modules/             # Core application modules
│   │   ├── core/            # Core functionality and event bus
│   │   ├── ui/              # User interface components
│   │   ├── webengine/       # Web view management
│   │   ├── appmanager/      # App management and storage
│   │   ├── session/         # Session and cookie management
│   │   ├── notifications/   # Notification system
│   │   ├── hotkeys/         # Global hotkey handling
│   │   ├── tabs/            # Tab management
│   │   └── updater/         # Auto-update functionality
│   ├── renderer/            # Renderer process (UI)
│   │   ├── panel.html       # Main panel HTML
│   │   ├── renderer.ts      # Renderer logic
│   │   └── preload.ts       # Preload script
│   └── shared/              # Shared types and utilities
│       └── types/           # TypeScript type definitions
├── dist/                    # Built application files
├── documents/               # Project documentation
├── webpack.*.config.js      # Webpack configuration
├── tsconfig.json           # TypeScript configuration
└── package.json            # Project dependencies and scripts
```

## 🏗️ Architecture Overview

SideView follows a modular Electron architecture:

```
┌─────────────────────────────────────────────┐
│              Main Process                   │
│         (Node.js + Electron)               │
└─────────────────┬───────────────────────────┘
                  │ IPC Communication
┌─────────────────▼───────────────────────────┐
│            Renderer Process                 │
│         (Chromium + Web UI)                │
└─────────────────────────────────────────────┘

Core Modules:
┌─────────────┬─────────────┬─────────────┐
│    Core     │     UI      │  WebEngine  │
│   Module    │   Module    │   Module    │
└─────────────┴─────────────┴─────────────┘
┌─────────────┬─────────────┬─────────────┐
│ AppManager  │ SessionMgr  │ Notifications│
│   Module    │   Module    │   Module    │
└─────────────┴─────────────┴─────────────┘
┌─────────────┬─────────────┬─────────────┐
│  Hotkeys    │    Tabs     │  Updater    │
│   Module    │   Module    │   Module    │
└─────────────┴─────────────┴─────────────┘
```

### Technology Stack
- **Electron**: Cross-platform desktop app framework
- **TypeScript**: Type-safe JavaScript development
- **Webpack**: Module bundling and build system
- **Better SQLite3**: Fast, embedded database
- **Electron Store**: Persistent configuration storage
- **Node Fetch**: HTTP client for web requests
- **Cheerio**: Server-side HTML parsing for favicon extraction
- **Jest**: Testing framework

## 🔧 Troubleshooting

### Common Issues

#### **Application Won't Start**
1. Ensure Node.js 18+ is installed
2. Run `npm install` to install dependencies
3. Run `npm run build` before `npm run dev`
4. Check console for error messages

#### **Hotkeys Not Working**
1. Check if another application is using the same hotkey
2. Try different hotkey combinations
3. Restart the application
4. Check system permissions

#### **High Memory Usage**
1. Restart the application
2. Reduce number of active apps
3. Clear browser cache for individual apps
4. Check for memory leaks in web applications

#### **Panel Not Appearing**
1. Check if panel is pinned and moved off-screen
2. Reset panel position in settings
3. Try different screen edge activation
4. Verify hotkey configuration

## 📊 Performance

### Target Metrics
- **Startup Time**: <3 seconds
- **Memory Usage**: <200MB base + ~50MB per active app
- **Panel Animation**: 60fps smooth transitions
- **Response Time**: <100ms for user interactions

### Optimization Tips
- Use session isolation only when needed
- Regularly restart the application
- Monitor memory usage in Task Manager
- Close unused web applications

## 🔐 Security & Privacy

### Security Features
- **Session Isolation**: Cookies/cache separated between apps
- **Electron Security**: Sandboxed renderer processes
- **Local Data Storage**: All data stored locally, no cloud sync
- **HTTPS Enforcement**: Secure connections for web apps
- **No Elevation Required**: Runs with standard user permissions

### Privacy Considerations
- **No Telemetry**: SideView doesn't collect usage data
- **Local Storage**: All data remains on your device
- **Web App Privacy**: Individual web app privacy policies apply
- **Update Checks**: Only checks GitHub for new releases

### Data Locations
- **Configuration**: User data directory (OS-specific)
- **App Database**: `userData/apps.db`
- **Session Data**: `userData/sessions/`
- **Cache**: `userData/cache/`
- **Logs**: `userData/logs/`

## 🤝 Contributing

We welcome contributions! Here's how to get started:

### Development Workflow
1. **Fork** the repository on GitHub
2. **Clone** your fork locally:
   ```bash
   git clone https://github.com/your-username/sideview.git
   cd sideview
   ```
3. **Install dependencies**:
   ```bash
   npm install
   ```
4. **Create** a feature branch:
   ```bash
   git checkout -b feature/amazing-feature
   ```
5. **Make your changes** and test thoroughly
6. **Run tests and linting**:
   ```bash
   npm run test
   npm run lint
   ```
7. **Commit** your changes:
   ```bash
   git commit -m 'Add amazing feature'
   ```
8. **Push** to your fork:
   ```bash
   git push origin feature/amazing-feature
   ```
9. **Open** a Pull Request on GitHub

### Code Style
- Follow TypeScript best practices
- Use ESLint configuration provided
- Write tests for new features
- Update documentation as needed

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Electron](https://electronjs.org/)
- Icons and design inspired by modern desktop applications
- Thanks to all contributors and testers

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/sideview/sideview/issues)
- **Discussions**: [GitHub Discussions](https://github.com/sideview/sideview/discussions)
- **Documentation**: See the `documents/` folder for detailed specs

---

**Made with ❤️ for productivity enthusiasts**
