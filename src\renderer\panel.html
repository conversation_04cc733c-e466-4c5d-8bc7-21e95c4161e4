<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SideView Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Default theme variables - will be overridden by theme manager */
            --panel-background: #2d2d30;
            --panel-background-secondary: #3e3e42;
            --panel-border: #464647;
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --text-muted: #999999;
            --accent-color: #0078d4;
            --accent-hover: #106ebe;
            --button-background: #0e639c;
            --button-hover: #1177bb;
            --button-text: #ffffff;
            --app-item-hover: rgba(255, 255, 255, 0.1);
            --app-item-active: rgba(0, 120, 212, 0.3);
            --scrollbar-track: rgba(255, 255, 255, 0.1);
            --scrollbar-thumb: rgba(255, 255, 255, 0.3);
            --scrollbar-thumb-hover: rgba(255, 255, 255, 0.5);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--panel-background);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            margin: 0;
            padding: 0;
        }

        .panel-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            border-radius: 16px 0 0 16px;
            border: 2px solid var(--panel-border);
            border-right: none;
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.4), 
                        -2px 0 10px rgba(0, 0, 0, 0.2),
                        inset 1px 1px 0 rgba(255, 255, 255, 0.1);
            overflow: hidden;
            background: linear-gradient(135deg, var(--panel-background) 0%, var(--panel-background-secondary) 100%);
        }

        .panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 32px; /* Reduced from 60px to 32px */
            padding: 0 8px; /* Reduced padding to maintain proportions */
            background: linear-gradient(135deg,
                        rgba(255, 255, 255, 0.08) 0%,
                        rgba(255, 255, 255, 0.02) 50%,
                        transparent 100%);
            border-bottom: 1px solid var(--panel-border);
            border-radius: 16px 0 0 0;
            -webkit-app-region: drag;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            box-sizing: border-box; /* Include border in height calculation */
            line-height: 32px; /* Match the new height */
        }

        .panel-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, 
                        transparent 0%, 
                        rgba(255, 255, 255, 0.2) 50%, 
                        transparent 100%);
        }

        .panel-title {
            font-size: 13px; /* Reduced from 15px to maintain proportions */
            font-weight: 600;
            color: var(--text-primary);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.3px;
        }

        .panel-controls {
            display: flex;
            gap: 6px; /* Reduced from 10px to maintain proportions */
            -webkit-app-region: no-drag;
        }

        .control-button {
            width: 24px; /* Reduced from 32px to 24px */
            height: 24px; /* Reduced from 32px to 24px */
            border: none;
            border-radius: 6px; /* Proportionally reduced from 8px */
            background: linear-gradient(135deg, var(--button-background) 0%,
                        color-mix(in srgb, var(--button-background) 80%, black) 100%);
            color: var(--button-text);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px; /* Reduced from 13px to maintain proportions */
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), /* Proportionally reduced shadows */
                        0 1px 2px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.15);
            position: relative;
            overflow: hidden;
        }

        .control-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, 
                        transparent, 
                        rgba(255, 255, 255, 0.2), 
                        transparent);
            transition: left 0.5s ease;
        }

        .control-button:hover {
            background: linear-gradient(135deg, var(--button-hover) 0%,
                        color-mix(in srgb, var(--button-hover) 80%, black) 100%);
            transform: translateY(-1px) scale(1.05); /* Reduced translateY from -2px to -1px */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25), /* Proportionally reduced shadows */
                        0 2px 4px rgba(0, 0, 0, 0.15),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .control-button:hover::before {
            left: 100%;
        }

        .control-button:active {
            transform: translateY(-1px) scale(1.02);
        }

        .control-button.pinned {
            background: linear-gradient(135deg, var(--accent-color) 0%, 
                        color-mix(in srgb, var(--accent-color) 80%, black) 100%);
            box-shadow: 0 4px 12px rgba(0, 120, 212, 0.4),
                        0 2px 6px rgba(0, 120, 212, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .app-list {
            flex: 0 0 auto;
            max-height: 200px;
            overflow-y: auto;
            border-bottom: 1px solid var(--panel-border);
        }

        .app-item {
            display: flex;
            align-items: center;
            padding: 14px 18px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: hidden;
            border-radius: 0;
        }

        .app-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 3px;
            height: 100%;
            background: transparent;
            transition: all 0.3s ease;
        }

        .app-item:hover {
            background: linear-gradient(90deg, 
                        var(--app-item-hover) 0%, 
                        rgba(255, 255, 255, 0.05) 100%);
            transform: translateX(4px);
            box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.05);
        }

        .app-item:hover::before {
            background: linear-gradient(180deg, 
                        var(--accent-color) 0%, 
                        color-mix(in srgb, var(--accent-color) 70%, white) 100%);
        }

        .app-item.active {
            background: linear-gradient(90deg, 
                        var(--app-item-active) 0%, 
                        rgba(0, 120, 212, 0.15) 100%);
            border-left: none;
            box-shadow: inset 4px 0 0 var(--accent-color), 
                        0 2px 12px rgba(0, 120, 212, 0.25),
                        inset 0 0 30px rgba(0, 120, 212, 0.1);
        }

        .app-item.active::before {
            background: linear-gradient(180deg, 
                        var(--accent-color) 0%, 
                        color-mix(in srgb, var(--accent-color) 70%, white) 100%);
            width: 4px;
        }

        .app-icon {
            width: 28px;
            height: 28px;
            border-radius: 6px;
            margin-right: 14px;
            background: linear-gradient(135deg, 
                        rgba(255, 255, 255, 0.1) 0%, 
                        rgba(255, 255, 255, 0.05) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .app-item:hover .app-icon {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .app-info {
            flex: 1;
            min-width: 0;
        }

        .app-name {
            font-size: 13px;
            font-weight: 500;
            color: var(--text-primary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .app-url {
            font-size: 11px;
            color: var(--text-secondary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .app-badge {
            background: #ff4444;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
            min-width: 16px;
            text-align: center;
        }

        /* Main Content Layout */
        .main-content {
            position: relative;
            flex: 1;
            overflow: hidden;
        }

        /* Vertical Tab Strip Styles */
        .tab-strip {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 48px; /* Width for collapsed state (40px hit target + 8px spacing) */
            background: linear-gradient(180deg,
                        var(--panel-background-secondary) 0%,
                        color-mix(in srgb, var(--panel-background-secondary) 90%, white) 50%,
                        var(--panel-background-secondary) 100%);
            border-right: 1px solid var(--panel-border);
            z-index: 1000; /* High z-index to ensure it stays above web content */
            transition: width 0.3s ease;
            overflow: visible;
        }

        .tab-strip-icons {
            display: flex;
            flex-direction: column;
            padding: 8px 4px;
            gap: 8px;
            width: 100%;
            box-sizing: border-box;
        }

        .tab-strip-flyout {
            position: absolute;
            left: 100%;
            top: 0;
            bottom: 0;
            width: 200px;
            background: var(--panel-background);
            border: 1px solid var(--panel-border);
            border-left: none;
            border-radius: 0 8px 8px 0;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
            transform: translateX(-100%);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1100; /* Higher than tab strip to ensure flyout appears above everything */
        }

        .tab-strip.flyout-visible .tab-strip-flyout,
        .tab-strip-flyout.flyout-visible {
            transform: translateX(0);
            opacity: 1;
            visibility: visible;
        }

        .tab-strip-content {
            padding: 12px;
            height: 100%;
            overflow-y: auto;
        }

        /* Tab Icon Styles (for collapsed state) */
        .tab-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg,
                        var(--panel-background) 0%,
                        color-mix(in srgb, var(--panel-background) 95%, white) 100%);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15),
                        0 1px 2px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .tab-icon:hover {
            background: linear-gradient(135deg,
                        var(--app-item-hover) 0%,
                        color-mix(in srgb, var(--app-item-hover) 80%, white) 100%);
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2),
                        0 2px 4px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }

        .tab-icon.active {
            background: linear-gradient(135deg,
                        var(--accent-color) 0%,
                        color-mix(in srgb, var(--accent-color) 85%, white) 100%);
            color: var(--button-text);
            border-color: var(--accent-color);
            box-shadow: 0 4px 16px rgba(0, 120, 212, 0.35),
                        0 2px 6px rgba(0, 120, 212, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.25);
        }

        .tab-icon-favicon {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        /* Tooltip styles for tab icons */
        .tab-icon[title]:hover::after {
            content: attr(title);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            margin-left: 8px;
            background: var(--panel-background);
            color: var(--text-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            border: 1px solid var(--panel-border);
            z-index: 1200; /* Higher than flyout to ensure tooltips appear above everything */
            pointer-events: none;
        }

        /* Tab Styles (for flyout content) */
        .tab {
            display: flex;
            align-items: center;
            background: linear-gradient(135deg,
                        var(--panel-background) 0%,
                        color-mix(in srgb, var(--panel-background) 95%, white) 100%);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 8px 12px;
            margin-bottom: 4px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            white-space: nowrap;
            overflow: hidden;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15),
                        0 1px 2px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .tab:hover {
            background: linear-gradient(135deg,
                        var(--app-item-hover) 0%,
                        color-mix(in srgb, var(--app-item-hover) 80%, white) 100%);
            transform: translateX(2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2),
                        0 2px 4px rgba(0, 0, 0, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }

        .tab.active {
            background: linear-gradient(135deg,
                        var(--accent-color) 0%,
                        color-mix(in srgb, var(--accent-color) 85%, white) 100%);
            color: var(--button-text);
            border-color: var(--accent-color);
            box-shadow: 0 4px 16px rgba(0, 120, 212, 0.35),
                        0 2px 6px rgba(0, 120, 212, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.25);
        }

        /* Content Area Styles */
        .content-area {
            position: absolute;
            left: 48px; /* Start immediately after tab strip */
            top: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            z-index: 1; /* Lower than tab strip */
        }

        .tab-favicon {
            width: 18px;
            height: 18px;
            margin-right: 10px;
            flex-shrink: 0;
            border-radius: 3px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .tab-title {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 13px;
            line-height: 1.2;
            font-weight: 500;
        }

        .tab-close {
            width: 20px;
            height: 20px;
            margin-left: 10px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: inherit;
            cursor: pointer;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            opacity: 0.7;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .tab-close:hover {
            opacity: 1;
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        }

        .tab.active .tab-close:hover {
            background: rgba(0, 0, 0, 0.2);
        }

        .tab-loading {
            width: 18px;
            height: 18px;
            margin-left: 6px;
            margin-right: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            animation: spin 1s linear infinite;
            flex-shrink: 0;
            color: var(--accent-color);
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }



        .web-content {
            flex: 1;
            background: linear-gradient(135deg,
                        #ffffff 0%,
                        color-mix(in srgb, #ffffff 98%, var(--panel-background)) 100%);
            border-radius: 0 0 0 16px;
            overflow: hidden;
            position: relative;
            z-index: 1; /* Lower z-index to stay below tab strip */
            box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.1),
                        inset 0 1px 2px rgba(0, 0, 0, 0.05),
                        0 -2px 4px rgba(0, 0, 0, 0.05);
        }

        /* Modal overlay styles for settings and other dialogs */
        .settings-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 10000; /* Very high z-index to ensure it's above everything */
            display: flex;
            align-items: center;
            justify-content: center;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .settings-dialog {
            background: var(--panel-background);
            border: 2px solid var(--panel-border);
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5),
                        0 8px 24px rgba(0, 0, 0, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.1);
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            position: relative;
            z-index: 10001; /* Higher than overlay */
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .web-view {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 0 0 0 16px;
        }

        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            background: rgba(255, 255, 255, 0.9);
            padding: 20px 32px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1),
                        0 2px 6px rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .no-apps-message {
            padding: 40px 20px;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            background: linear-gradient(135deg, 
                        rgba(255, 255, 255, 0.02) 0%, 
                        rgba(255, 255, 255, 0.05) 100%);
            border-radius: 12px;
            margin: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .add-app-button {
            background: linear-gradient(135deg, 
                        var(--accent-color) 0%, 
                        color-mix(in srgb, var(--accent-color) 85%, black) 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            margin-top: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 3px 8px rgba(0, 120, 212, 0.3),
                        0 1px 3px rgba(0, 120, 212, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .add-app-button:hover {
            background: linear-gradient(135deg, 
                        var(--button-hover) 0%, 
                        color-mix(in srgb, var(--button-hover) 85%, black) 100%);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 16px rgba(0, 120, 212, 0.4),
                        0 2px 6px rgba(0, 120, 212, 0.25),
                        inset 0 1px 0 rgba(255, 255, 255, 0.25);
        }

        .add-app-button:active {
            transform: translateY(-1px) scale(1.02);
        }

        /* Scrollbar styling */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--scrollbar-track);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--scrollbar-thumb);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--scrollbar-thumb-hover);
        }
    </style>
</head>
<body>
    <div class="panel-container">
        <!-- Panel Header -->
        <div class="panel-header">
            <div class="panel-title">SideView</div>
            <div class="panel-controls">
                <button class="control-button" id="new-tab-button" title="New Tab" aria-label="New Tab">+</button>
                <button class="control-button" id="pin-button" title="Pin Panel">📌</button>
                <button class="control-button" id="settings-button" title="Settings">⚙️</button>
                <button class="control-button" id="close-button" title="Close">✕</button>
            </div>
        </div>

        <!-- Main Content Area with Tab Strip -->
        <div class="main-content">
            <!-- Vertical Tab Strip -->
            <div class="tab-strip" id="tab-strip">
                <div class="tab-strip-icons" id="tab-strip-icons">
                    <!-- Tab icons will be dynamically added here -->
                </div>
                <div class="tab-strip-flyout" id="tab-strip-flyout">
                    <div class="tab-strip-content" id="tab-strip-content">
                        <!-- Tab content will be shown here on hover -->
                    </div>
                </div>
            </div>

            <!-- Content Area -->
            <div class="content-area">
                <!-- App List (Legacy - keeping for compatibility) -->
                <div class="app-list" id="app-list" style="display: none;">
                    <div class="no-apps-message" id="no-apps-message">
                        <div>No web apps added yet</div>
                        <button class="add-app-button" id="add-app-button">Add Web App</button>
                    </div>
                </div>

                <!-- Web Content Area -->
                <div class="web-content" id="web-content">
                    <div class="loading-indicator" id="loading-indicator">Loading...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
