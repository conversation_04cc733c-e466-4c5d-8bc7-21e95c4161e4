/**
 * Preload script for secure IPC communication
 * Exposes safe APIs to the renderer process
 */

import { contextB<PERSON>, ipcRenderer } from 'electron';
import { WebAppModel, CreateAppRequest, UpdateAppRequest, AppSettings, TabInfo, CreateTabRequest, TabManagerState } from '@shared/types/app.types';

// Define the API interface
interface SideViewAPI {
  // Panel control
  panel: {
    show(): Promise<void>;
    hide(): Promise<void>;
    toggle(): Promise<void>;
    setPinned(pinned: boolean): Promise<void>;
    isVisible(): Promise<boolean>;
    isPinned(): Promise<boolean>;
    setModalState: (isModalOpen: boolean) => Promise<void>;
    restartWidget(): Promise<void>;
    attachActiveWebApp(): Promise<void>; // **CRITICAL FIX: Add immediate WebApp attachment**
    shrinkBrowserViewForOverlay(): Promise<void>; // For tab preview overlays
    restoreBrowserViewFromOverlay(): Promise<void>; // Restore after overlay
  };

  // App management
  apps: {
    getAll(): Promise<WebAppModel[]>;
    create(request: CreateAppRequest): Promise<WebAppModel>;
    update(request: UpdateAppRequest): Promise<WebAppModel>;
    delete(appId: string): Promise<void>;
    activate(appId: string): Promise<void>;
  };

  // Tab management
  tabs: {
    getAll(): Promise<TabInfo[]>;
    getActive(): Promise<TabInfo | null>;
    create(request: CreateTabRequest): Promise<TabInfo>;
    close(tabId: string): Promise<void>;
    activate(tabId: string): Promise<void>;
    navigate(tabId: string, url: string): Promise<void>;
    reload(tabId: string): Promise<void>;
    goBack(tabId: string): Promise<void>;
    goForward(tabId: string): Promise<void>;
    getState(): Promise<TabManagerState>;
  };

  // Configuration
  config: {
    get(): Promise<AppSettings>;
    update(updates: Partial<AppSettings>): Promise<void>;
  };

  // System info
  system: {
    getDisplays(): Promise<any[]>;
    getPrimaryDisplay(): Promise<any>;
  };

  // Theme management
  theme: {
    get(): Promise<{ theme: string; isDark: boolean; variables: Record<string, string> }>;
    set(theme: string): Promise<void>;
    getVariables(): Promise<Record<string, string>>;
  };

  // Event listeners
  on(channel: string, listener: (...args: any[]) => void): void;
  off(channel: string, listener: (...args: any[]) => void): void;
}

// Implement the API
const sideViewAPI: SideViewAPI = {
  panel: {
    show: () => ipcRenderer.invoke('panel:show'),
    hide: () => ipcRenderer.invoke('panel:hide'),
    toggle: () => ipcRenderer.invoke('panel:toggle'),
    setPinned: (pinned: boolean) => ipcRenderer.invoke('ui:setPinned', pinned),
    isVisible: () => ipcRenderer.invoke('ui:isVisible'),
    isPinned: () => ipcRenderer.invoke('ui:isPinned'),
    setModalState: (isModalOpen: boolean) => ipcRenderer.invoke('ui:setModalState', isModalOpen),
    restartWidget: () => ipcRenderer.invoke('ui:restartWidget'),
    attachActiveWebApp: () => ipcRenderer.invoke('ui:attachActiveWebApp'), // **CRITICAL FIX: Immediate WebApp attachment**
    shrinkBrowserViewForOverlay: () => ipcRenderer.invoke('ui:shrinkBrowserViewForOverlay'), // For tab preview overlays
    restoreBrowserViewFromOverlay: () => ipcRenderer.invoke('ui:restoreBrowserViewFromOverlay') // Restore after overlay
  },

  apps: {
    getAll: () => ipcRenderer.invoke('apps:getAll'),
    create: (request: CreateAppRequest) => ipcRenderer.invoke('apps:create', request),
    update: (request: UpdateAppRequest) => ipcRenderer.invoke('apps:update', request),
    delete: (appId: string) => ipcRenderer.invoke('apps:delete', appId),
    activate: (appId: string) => ipcRenderer.invoke('apps:activate', appId)
  },

  tabs: {
    getAll: () => ipcRenderer.invoke('tabs:getAll'),
    getActive: () => ipcRenderer.invoke('tabs:getActive'),
    create: (request: CreateTabRequest) => ipcRenderer.invoke('tabs:create', request),
    close: (tabId: string) => ipcRenderer.invoke('tabs:close', tabId),
    activate: (tabId: string) => ipcRenderer.invoke('tabs:activate', tabId),
    navigate: (tabId: string, url: string) => ipcRenderer.invoke('tabs:navigate', tabId, url),
    reload: (tabId: string) => ipcRenderer.invoke('tabs:reload', tabId),
    goBack: (tabId: string) => ipcRenderer.invoke('tabs:goBack', tabId),
    goForward: (tabId: string) => ipcRenderer.invoke('tabs:goForward', tabId),
    getState: () => ipcRenderer.invoke('tabs:getState')
  },

  config: {
    get: () => ipcRenderer.invoke('config:get'),
    update: (updates: Partial<AppSettings>) => ipcRenderer.invoke('config:update', updates)
  },

  system: {
    getDisplays: () => ipcRenderer.invoke('system:getDisplays'),
    getPrimaryDisplay: () => ipcRenderer.invoke('system:getPrimaryDisplay')
  },

  theme: {
    get: () => ipcRenderer.invoke('theme:get'),
    set: (theme: string) => ipcRenderer.invoke('theme:set', theme),
    getVariables: () => ipcRenderer.invoke('theme:getVariables')
  },

  on: (channel: string, listener: (...args: any[]) => void) => {
    // Validate allowed channels
    const allowedChannels = [
      'app-created',
      'app-updated',
      'app-deleted',
      'app-activated',
      'config-changed',
      'panel-shown',
      'panel-hidden',
      'notification-received',
      'tab-created',
      'tab-closed',
      'tab-activated',
      'tab-updated',
      'tab-navigation-completed',
      'tab-title-changed',
      'tab-favicon-changed',
      'tab-loading-state-changed'
    ];

    if (allowedChannels.includes(channel)) {
      ipcRenderer.on(channel, listener);
    } else {
      console.warn(`Channel '${channel}' is not allowed`);
    }
  },

  off: (channel: string, listener: (...args: any[]) => void) => {
    ipcRenderer.off(channel, listener);
  }
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('sideView', sideViewAPI);

// Also expose a type declaration for TypeScript
declare global {
  interface Window {
    sideView: SideViewAPI;
  }
}
