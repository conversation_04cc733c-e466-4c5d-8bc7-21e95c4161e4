/**
 * Core application types and interfaces
 * Migrated from C# SideView.AppManager and SideView.WebEngine models
 */

export interface WebAppModel {
  id: string;
  name: string;
  url: string;
  sessionMode: SessionMode;
  isActive: boolean;
  lastAccessed: Date;
  userDataPath?: string;
  iconPath?: string;
  order: number;
  isEnabled: boolean;
  createdAt: Date;
  updatedAt: Date;
  badgeCount?: number;
  hasBadge?: boolean;
  // Tab management properties
  isTab?: boolean;
  tabIndex?: number;
  favicon?: string;
  title?: string;
}

export enum SessionMode {
  Shared = 'shared',
  Isolated = 'isolated'
}

export interface CreateAppRequest {
  name: string;
  url: string;
  sessionMode: SessionMode;
}

export interface UpdateAppRequest {
  id: string;
  name?: string;
  url?: string;
  sessionMode?: SessionMode;
  order?: number;
  isEnabled?: boolean;
}

// Tab management interfaces
export interface TabInfo {
  id: string;
  title: string;
  url: string;
  favicon?: string;
  isActive: boolean;
  isLoading: boolean;
  canGoBack: boolean;
  canGoForward: boolean;
}

export interface CreateTabRequest {
  url?: string;
  name?: string;
  sessionMode?: SessionMode;
  activate?: boolean;
}

export interface TabManagerState {
  tabs: TabInfo[];
  activeTabId: string | null;
  maxTabs: number;
}

// Edge activation zone configuration
export interface ActivationZoneConfig {
  enabled: boolean;
  position: 'top' | 'middle' | 'bottom' | 'full';
  size: number; // Percentage of screen edge (0-100)
  width: number; // Activation zone width in pixels
  offset: number; // Offset from edge position in pixels
}

export interface EdgeActivationSettings {
  left: ActivationZoneConfig;
  right: ActivationZoneConfig;
  top: ActivationZoneConfig;
  bottom: ActivationZoneConfig;
}

export interface AppValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface FaviconData {
  url: string;
  localPath: string;
  data: Buffer;
  mimeType: string;
}

// Event types
export interface AppEventArgs {
  app: WebAppModel;
}

export interface NavigationEventArgs {
  url: string;
  isSuccess: boolean;
  error?: string;
}

export interface TitleChangedEventArgs {
  title: string;
  url: string;
}

// Configuration types
export interface AppSettings {
  ui: UISettings;
  webEngine: WebEngineSettings;
  notifications: NotificationSettings;
  hotkeys: HotkeySettings;
  updater: UpdaterSettings;
}

export interface UISettings {
  panelWidth: number;
  panelHeight: number;
  panelPosition: PanelPosition;
  theme: Theme;
  animationDuration: number;
  autoHide: boolean;
  activationDelay: number;
  edgeActivation: EdgeActivationSettings;
}

export interface WebEngineSettings {
  userAgent: string;
  enableDevTools: boolean;
  enableJavaScript: boolean;
  enableImages: boolean;
  enablePlugins: boolean;
  defaultZoomLevel: number;
}

export interface NotificationSettings {
  enabled: boolean;
  showBadges: boolean;
  soundEnabled: boolean;
  position: NotificationPosition;
}

export interface HotkeySettings {
  togglePanel: string;
  newTab: string;
  refresh: string;
  enabled: boolean;
}

export interface UpdaterSettings {
  enabled: boolean;
  checkInterval: number;
  autoDownload: boolean;
  autoInstall: boolean;
  channel: UpdateChannel;
}

export enum PanelPosition {
  Left = 'left',
  Right = 'right'
}

export enum Theme {
  Light = 'light',
  Dark = 'dark',
  System = 'system'
}

export enum NotificationPosition {
  TopRight = 'top-right',
  TopLeft = 'top-left',
  BottomRight = 'bottom-right',
  BottomLeft = 'bottom-left'
}

export enum UpdateChannel {
  Stable = 'stable',
  Beta = 'beta',
  Alpha = 'alpha'
}
